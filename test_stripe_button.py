#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Stripe按钮功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from ui.dialogs.account_details_dialog import AccountDetailsDialog

def test_stripe_button():
    """测试Stripe按钮功能"""
    app = QApplication(sys.argv)
    
    # 模拟账户数据
    test_account_data = {
        "email": "<EMAIL>",
        "password": "test_password",
        "notes": "测试账户",
        "auth_info": {
            "cursorAuth/accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.K0lZkK3JLrtk7EWaGvBKX6apwF4ZHMQWSrT4T0Qkzz0"
        }
    }
    
    # 模拟主窗口
    class MockMainWindow:
        def show_toast(self, message, error=False):
            print(f"Toast: {message} (error={error})")
    
    main_window = MockMainWindow()
    
    # 创建账户详情对话框
    dialog = AccountDetailsDialog(test_account_data, main_window)
    
    # 显示对话框
    dialog.exec()
    
    app.quit()

if __name__ == "__main__":
    test_stripe_button()
