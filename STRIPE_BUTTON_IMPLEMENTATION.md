# Stripe URL 按钮实现说明

## 功能概述

在账户详情对话框中添加了一个"打开StripeURL"按钮，点击后可以获取该账户的Stripe订阅管理页面URL并在浏览器中打开。

## 实现细节

### 1. 新增的文件修改

**文件：** `ui/dialogs/account_details_dialog.py`

### 2. 主要功能

1. **按钮添加**：在账户详情对话框的按钮区域添加了"打开StripeURL"按钮
2. **异步处理**：使用QThread避免GUI阻塞
3. **Token获取**：从账户的auth_info中获取accessToken
4. **URL获取**：调用Cursor API获取Stripe会话URL
5. **浏览器打开**：自动在默认浏览器中打开Stripe页面

### 3. 技术实现

#### 3.1 工作线程类
```python
class StripeUrlWorker(QThread):
    """获取Stripe URL的工作线程"""
    
    # 定义信号
    url_fetched = Signal(str)  # 成功获取URL
    error_occurred = Signal(str)  # 发生错误
```

#### 3.2 按钮样式
- 使用Stripe品牌色 (#6772E5)
- 悬停效果 (#5469D4)
- 与其他按钮保持一致的设计风格

#### 3.3 Token构建逻辑
参考了 `test/cancel_subscription_final.py` 和账户额度获取的逻辑：
```python
prefix = 'user_01000000000000000000000000%3A%3A'
full_token = prefix + access_token
```

### 4. 用户体验优化

1. **非阻塞操作**：网络请求在后台线程执行，GUI保持响应
2. **状态反馈**：显示"正在获取Stripe URL..."的提示信息
3. **错误处理**：网络错误或Token缺失时显示相应错误信息
4. **成功反馈**：成功打开浏览器后显示确认信息

### 5. 错误处理

- Token缺失：提示"无法获取账户Token，请确保账户信息完整"
- 网络错误：显示具体的网络错误信息
- API错误：显示HTTP状态码和响应信息

### 6. 依赖关系

新增的导入模块：
- `requests`：用于HTTP请求
- `webbrowser`：用于打开浏览器
- `QThread`, `Signal`：用于异步处理

### 7. 使用方法

1. 在账户管理页面点击任意账户行，打开账户详情对话框
2. 在对话框中点击"打开StripeURL"按钮
3. 等待系统获取Stripe URL（界面不会卡死）
4. 成功后会自动在浏览器中打开Stripe订阅管理页面

### 8. 注意事项

- 需要账户具有有效的accessToken
- 需要网络连接正常
- 依赖Cursor的API接口 `https://cursor.com/api/stripeSession`

## 测试

创建了测试脚本 `test_stripe_button.py` 用于验证功能的正确性。

## 问题解决

**原问题**：点击按钮后GUI卡死，直到网络请求完成才恢复响应。

**解决方案**：使用QThread将网络请求移到后台线程，通过信号槽机制与主线程通信，确保GUI始终保持响应。
